import React from 'react';
import useMicrophone from './useMicrophone';

/**
 * Demo component để test các c<PERSON>i tiến trong useMicrophone hook
 * Đặc biệt test việc xử lý rapid start/stop calls
 */
const MicrophoneDemo: React.FC = () => {
  const {
    startMicrophone,
    stopMicrophone,
    cancelRecording,
    isStartMicrophone,
    audioBlob,
    isSpeaking,
    recordTime,
    transcript,
    isEnglish,
    getCurrentTranscript
  } = useMicrophone();

  const handleRapidStartStop = () => {
    console.log('=== Testing Rapid Start/Stop ===');
    
    // Simulate rapid clicks
    startMicrophone();
    setTimeout(() => startMicrophone(), 50);  // Should be ignored
    setTimeout(() => startMicrophone(), 100); // Should be ignored
    setTimeout(() => stopMicrophone(), 150);  // Should be ignored (too soon)
    setTimeout(() => stopMicrophone(), 600);  // Should work (after min duration)
  };

  const handleNormalFlow = () => {
    console.log('=== Testing Normal Flow ===');
    if (isStartMicrophone) {
      stopMicrophone();
    } else {
      startMicrophone();
    }
  };

  const handleCancel = () => {
    console.log('=== Testing Cancel ===');
    cancelRecording();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>🎤 Microphone Hook Demo</h2>
      
      {/* Status Display */}
      <div style={{ 
        background: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>📊 Status</h3>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
          <div>
            <strong>Recording:</strong> 
            <span style={{ 
              color: isStartMicrophone ? '#22c55e' : '#ef4444',
              marginLeft: '8px'
            }}>
              {isStartMicrophone ? '🔴 Active' : '⚫ Inactive'}
            </span>
          </div>
          <div>
            <strong>Speaking:</strong> 
            <span style={{ 
              color: isSpeaking ? '#3b82f6' : '#6b7280',
              marginLeft: '8px'
            }}>
              {isSpeaking ? '🗣️ Yes' : '🤐 No'}
            </span>
          </div>
          <div>
            <strong>Duration:</strong> 
            <span style={{ marginLeft: '8px' }}>
              ⏱️ {formatTime(recordTime)}
            </span>
          </div>
          <div>
            <strong>Language:</strong> 
            <span style={{ marginLeft: '8px' }}>
              {isEnglish ? '🇺🇸 English' : '🇻🇳 Vietnamese'}
            </span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div style={{ marginBottom: '20px' }}>
        <h3>🎛️ Controls</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={handleNormalFlow}
            style={{
              padding: '10px 20px',
              backgroundColor: isStartMicrophone ? '#ef4444' : '#22c55e',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            {isStartMicrophone ? '⏹️ Stop Recording' : '▶️ Start Recording'}
          </button>
          
          <button
            onClick={handleRapidStartStop}
            style={{
              padding: '10px 20px',
              backgroundColor: '#f59e0b',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            ⚡ Test Rapid Start/Stop
          </button>
          
          <button
            onClick={handleCancel}
            style={{
              padding: '10px 20px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            ❌ Cancel
          </button>
        </div>
      </div>

      {/* Transcript Display */}
      {transcript && (
        <div style={{ marginBottom: '20px' }}>
          <h3>📝 Transcript</h3>
          <div style={{
            background: '#f8fafc',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            padding: '15px',
            minHeight: '60px',
            fontFamily: 'monospace'
          }}>
            {transcript || 'No transcript yet...'}
          </div>
          <small style={{ color: '#6b7280' }}>
            Current transcript: {getCurrentTranscript()}
          </small>
        </div>
      )}

      {/* Audio Blob Info */}
      {audioBlob && (
        <div style={{ marginBottom: '20px' }}>
          <h3>🎵 Audio Data</h3>
          <div style={{
            background: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '8px',
            padding: '15px'
          }}>
            <div><strong>Size:</strong> {(audioBlob.size / 1024).toFixed(2)} KB</div>
            <div><strong>Type:</strong> {audioBlob.type}</div>
            <div><strong>Valid:</strong> {audioBlob.size > 0 ? '✅ Yes' : '❌ No'}</div>
          </div>
        </div>
      )}

      {/* Debug Info */}
      <div style={{ marginTop: '30px' }}>
        <h3>🐛 Debug Info</h3>
        <div style={{
          background: '#1f2937',
          color: '#f9fafb',
          padding: '15px',
          borderRadius: '8px',
          fontFamily: 'monospace',
          fontSize: '12px',
          maxHeight: '200px',
          overflow: 'auto'
        }}>
          <div>Check browser console for detailed logs</div>
          <div>• Debounce protection: 300ms</div>
          <div>• Minimum recording duration: 500ms</div>
          <div>• Audio validation: Enabled</div>
          <div>• Error handling: Enhanced</div>
        </div>
      </div>

      {/* Instructions */}
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#6b7280' }}>
        <h4>📋 Test Instructions:</h4>
        <ol>
          <li><strong>Normal Flow:</strong> Click "Start Recording" then "Stop Recording" after a few seconds</li>
          <li><strong>Rapid Test:</strong> Click "Test Rapid Start/Stop" to simulate rapid user interactions</li>
          <li><strong>Error Handling:</strong> Try stopping immediately after starting (should be prevented)</li>
          <li><strong>Console Logs:</strong> Check browser console for detailed debug information</li>
        </ol>
      </div>
    </div>
  );
};

export default MicrophoneDemo;
