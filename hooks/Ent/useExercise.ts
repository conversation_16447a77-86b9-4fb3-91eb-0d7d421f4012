import { ApiEndpoints } from '@/configs';
import { AnswerFormat, QUESTION_CONFIGS } from '@/configs/Exercise';
import { QUERY_KEY } from '@/constant/query-key';
import {
  ExerciseResponse,
  ExercisesResponse,
  SubmitAnswerResponse,
} from '@/interfaces/exercise.interface';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export const useExercises = ({ paragraph_id }: { paragraph_id: number }) => {
  return useQuery({
    queryKey: [QUERY_KEY.EXERCISES, paragraph_id],
    queryFn: async (): Promise<ExercisesResponse> => {
      return await axiosConfig.get(`${ApiEndpoints.EXERCISES}`, {
        params: {
          paragraph_id,
        },
      });
    },
    staleTime: Infinity,
    enabled: !!paragraph_id,
  });
};

export const useExercise = ({
  question_id,
  question_format,
}: {
  question_id: number;
  question_format: string;
}) => {
  return useQuery({
    queryKey: [QUERY_KEY.EXERCISE, question_id, question_format],
    queryFn: async (): Promise<ExerciseResponse> => {
      return await axiosConfig.get(`${ApiEndpoints.EXERCISE}`, {
        params: {
          question_id,
          question_format,
        },
      });
    },
    staleTime: Infinity,
    enabled: !!question_id,
  });
};

export const useSubmitAnswerMutation = (
  options?: Omit<
    UseMutationOptions<
      SubmitAnswerResponse,
      AxiosError,
      {
        answers: {
          question_id: number;
          quiz_id: number;
          answer_id: number;
          question_format: keyof typeof QUESTION_CONFIGS;
          answer_format: AnswerFormat;
          right: number;
          content: string;
        }[];
        member_exercise_token?: string;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload) => {
      return axiosConfig.post(`${ApiEndpoints.ANSWERS}`, payload);
    },
    ...options,
  });
};
