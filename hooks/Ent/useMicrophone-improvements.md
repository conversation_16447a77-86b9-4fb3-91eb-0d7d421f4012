# useMicrophone Hook - <PERSON><PERSON><PERSON> tiến xử lý lỗi "Unable to decode audio data"

## 🔍 Vấn đề đã được gi<PERSON>i quyết

### Lỗi "Unable to decode audio data"
- **Nguyên nhân**: <PERSON><PERSON><PERSON> ra khi user start/stop recording quá nhanh, khiến audioBlob không hoàn chỉnh
- **Vị trí**: <PERSON>rong hàm `convertSampleRate()` tại `audioCtx.decodeAudioData(arrayBuffer)`
- **Tác động**: Crash ứng dụng khi thao tác nhanh

### Race Conditions
- **Vấn đề**: Nhiều MediaRecorder instances chạy đồng thời
- **Hậu quả**: Conflicts và memory leaks
- **Giải pháp**: Debounce và state validation

## 🛠️ Các cải tiến đã implement

### 1. **Debounce Mechanism**
```typescript
const DEBOUNCE_DELAY = 300; // 300ms delay gi<PERSON><PERSON> các actions
const MIN_RECORDING_DURATION = 500; // Minimum 500ms recording

const canPerformAction = useCallback(() => {
  const now = Date.now();
  const timeSinceLastAction = now - lastActionTimeRef.current;
  
  if (isOperatingRef.current) {
    console.warn('Operation already in progress, skipping...');
    return false;
  }
  
  if (timeSinceLastAction < DEBOUNCE_DELAY) {
    console.warn(`Action too soon, need to wait ${DEBOUNCE_DELAY - timeSinceLastAction}ms more`);
    return false;
  }
  
  return true;
}, []);
```

### 2. **Enhanced Error Handling cho Audio Decoding**
```typescript
const convertSampleRate = async (audioBlob: Blob, targetSampleRate: number) => {
  try {
    // Validate audio data first
    if (!validateAudioData(audioBlob)) {
      console.warn('Invalid audio data, returning original blob');
      return audioBlob;
    }

    const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
  } catch (decodeError) {
    console.error('Failed to decode audio data:', decodeError);
    console.warn('Returning original blob due to decode error');
    
    // Cleanup audio context
    if (audioCtx.state !== 'closed') {
      await audioCtx.close();
    }
    
    return audioBlob; // Return original blob as fallback
  }
};
```

### 3. **Audio Data Validation**
```typescript
const validateAudioData = useCallback((audioBlob: Blob): boolean => {
  if (!audioBlob || audioBlob.size === 0) {
    console.warn('Invalid audio blob: empty or null');
    return false;
  }

  if (audioBlob.size < 1000) { // Less than 1KB is likely invalid
    console.warn('Audio blob too small, likely invalid:', audioBlob.size);
    return false;
  }

  return true;
}, []);
```

### 4. **Comprehensive Resource Cleanup**
```typescript
const cleanupResources = useCallback(() => {
  // Stop speech recognition
  if (recognitionRef.current) {
    try {
      recognitionRef.current.stop();
    } catch (error) {
      console.warn('Error stopping speech recognition:', error);
    }
    recognitionRef.current = null;
  }

  // Stop media recorder
  if (mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
    try {
      mediaRecorder.current.stop();
    } catch (error) {
      console.warn('Error stopping media recorder:', error);
    }
  }

  // Release microphone
  if (userMediaStreamRef.current) {
    userMediaStreamRef.current.getTracks().forEach((track) => track.stop());
    userMediaStreamRef.current = null;
  }

  // Close audio context
  if (state.audioContext && state.audioContext.state !== 'closed') {
    try {
      state.audioContext.close();
    } catch (error) {
      console.warn('Error closing audio context:', error);
    }
    setAudioContext(null);
  }

  setAnalyser(null);
  isOperatingRef.current = false;
}, [state.audioContext]);
```

### 5. **State Validation & Race Condition Prevention**
```typescript
// Trong startMicrophone()
if (!canPerformAction()) {
  return;
}

// Check if already recording
if (state.isStartMicrophone || (mediaRecorder.current && mediaRecorder.current.state === 'recording')) {
  console.warn('Already recording, ignoring start request');
  return;
}

// Set operation flag
isOperatingRef.current = true;
lastActionTimeRef.current = Date.now();
recordingStartTimeRef.current = Date.now();
```

### 6. **Minimum Recording Duration Check**
```typescript
// Trong stopMicrophone()
if (!isStopByTimeOut && recordingStartTimeRef.current > 0) {
  const recordingDuration = Date.now() - recordingStartTimeRef.current;
  if (recordingDuration < MIN_RECORDING_DURATION) {
    console.warn(`Recording too short (${recordingDuration}ms), minimum is ${MIN_RECORDING_DURATION}ms`);
    return;
  }
}
```

## 🧪 Testing

Đã tạo comprehensive test suite để verify các cải tiến:

- ✅ Debounce protection cho rapid start/stop calls
- ✅ Minimum recording duration validation
- ✅ Audio decoding error handling
- ✅ Audio data validation
- ✅ Resource cleanup
- ✅ Concurrent operation prevention

## 📈 Kết quả

### Trước khi cải tiến:
- ❌ Crash khi start/stop quá nhanh
- ❌ Memory leaks từ uncleaned resources
- ❌ Race conditions với multiple MediaRecorder instances
- ❌ Không handle được invalid audio data

### Sau khi cải tiến:
- ✅ Graceful handling của rapid user interactions
- ✅ Proper resource cleanup
- ✅ Fallback mechanism cho audio decoding errors
- ✅ Comprehensive validation và error logging
- ✅ Improved user experience với debounce protection

## 🚀 Sử dụng

Hook vẫn giữ nguyên API, không cần thay đổi code existing:

```typescript
const {
  startMicrophone,
  stopMicrophone,
  cancelRecording,
  isStartMicrophone,
  audioBlob,
  // ... other properties
} = useMicrophone();
```

Các cải tiến hoạt động tự động trong background để protect khỏi race conditions và audio decoding errors.
