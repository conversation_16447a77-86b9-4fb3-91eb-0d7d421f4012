import React, { useState } from 'react';

import {
  AnswerFormat,
  AnswerType,
  IS_CORRECT,
  QUESTION_CONFIGS,
  QuestionDescription,
  QuestionDisplay,
  QuestionStatus,
} from '@/configs/Exercise';
import { QUERY_KEY } from '@/constant/query-key';
import { AnswersSkeleton } from '@/containers/learn/exercise/AnswersSkeleton';
import { NormalQuestion } from '@/containers/learn/exercise/NormalQuestion';
import { SortQuestion } from '@/containers/learn/exercise/SortQuestion';
import { Question } from '@/interfaces/exercise.interface';
import useBalanceStore from '@/store/balance';
import { useQueryClient } from '@tanstack/react-query';
import _ from 'lodash';
import { useTranslations } from 'next-intl';
import { toast } from 'react-hot-toast';

import WavePlayer from '@/components/Audio/WavePlayer';
import NextImageWithFallback from '@/components/NextImageWithFallback';

import { useExercise, useSubmitAnswerMutation } from '@/hooks/Ent/useExercise';
import useLearnStore from '@/store/learn';

const MOCK_AUDIO =
  'https://file.langenter.com/course/audio/2024/12/23/1734951254_MQo2_V_TDzcT1A.mp3';

export const useQuestion = ({
  questions,
  setExerciseProgress,
  setIsStarted,
}: {
  questions: Question[];
  setExerciseProgress: React.Dispatch<React.SetStateAction<number>>;
  setIsStarted: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { balance } = useBalanceStore();
  const { exerciseToken } = useLearnStore();

  const t = useTranslations();
  const queryClient = useQueryClient();
  const [questionStatus, setQuestionStatus] = useState<QuestionStatus | null>(null);
  const [activeQuestion, setActiveQuestion] = useState<Question>(questions[0]);
  const [selectedAnswer, setSelectedAnswer] = useState<number[]>([]);
  const [correctAnswers, setCorrectAnswers] = useState<number[]>([]);
  const [isFinish, setIsFinish] = useState<boolean>(false);

  const { data: answerData, isLoading: isLoadingAnswers } = useExercise({
    question_id: activeQuestion.id,
    question_format: activeQuestion.question_format,
  });
  console.log('🚀 ~ answerData:', answerData);

  const answers = answerData?.data?.answers || [];
  const submitAnswerMutation = useSubmitAnswerMutation();

  const resetQuestionState = () => {
    setSelectedAnswer([]);
    setCorrectAnswers([]);
    setQuestionStatus(null);
  };

  const handleNextQuestion = () => {
    if (!balance || balance <= 0) {
      toast.error(t('learn.balance_not_enough'));
      return;
    }
    const currentIndex = questions.findIndex((question) => question.id === activeQuestion.id);
    if (currentIndex < questions.length - 1) {
      setActiveQuestion(questions[currentIndex + 1]);
      setExerciseProgress((currentIndex + 1) / questions.length);
    } else {
      setExerciseProgress(1);
      setIsFinish(true);
    }
    resetQuestionState();
  };

  const handleDoExerciseAgain = () => {
    setIsStarted(false);
    setActiveQuestion(questions[0]);
    setExerciseProgress(0);
    setIsFinish(false);
    resetQuestionState();
  };

  const handleDoNewExercise = async () => {
    try {
      setIsStarted(false);
      setIsFinish(false);
      setExerciseProgress(0);
      await queryClient.invalidateQueries({ queryKey: ['exercises'] });
    } catch (error) {
      console.error(error);
      toast.error(error.message);
    }
  };

  const handleCheckAnswer = async () => {
    if (isFinish) return;
    if (selectedAnswer.length === 0) {
      toast.error(t('learn.no_selected_answers'));
      return;
    }

    if (!balance || balance <= 0) {
      toast.error(t('learn.balance_not_enough'));
      return;
    }

    try {
      const response = await submitAnswerMutation.mutateAsync({
        answers: selectedAnswer.map((answerId, index) => ({
          question_id: activeQuestion.id,
          quiz_id: activeQuestion.quiz_id,
          answer_id: answerId,
          question_format: activeQuestion.question_format,
          answer_format: activeQuestion.answer_format as AnswerFormat,
          right: activeQuestion.display === QuestionDisplay.SORT ? index : AnswerType.CORRECT,
          content: answers.find((answer) => answer.id === answerId)?.content || '',
        })),
        member_exercise_token: exerciseToken,
      });

      if (response.status === 200 && response.data.answers.length > 0) {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEY.BALANCE] });
        const correctAnswerIds = response.data.answers
          .filter((answer) => answer.right === AnswerType.CORRECT)
          .map((answer) => answer.id);

        setCorrectAnswers(correctAnswerIds);

        const isCorrect =
          activeQuestion.display === QuestionDisplay.SORT
            ? response.data.is_correct === IS_CORRECT
            : _.isEqual(selectedAnswer.sort(), correctAnswerIds.sort());

        setQuestionStatus(isCorrect ? QuestionStatus.CORRECT : QuestionStatus.WRONG);
      } else {
        setQuestionStatus(QuestionStatus.WRONG);
      }
    } catch (e: unknown) {
      const error = e as { response?: { data?: { message?: string } } };
      toast.error(error?.response?.data?.message || t('message.error.unknown'));
    }
  };

  const renderQuestionTitle = () => {
    const questionConfig = QUESTION_CONFIGS[activeQuestion.question_format];
    return (
      <div className="w-full">
        <p className="text-color-major text-lg font-normal mt-2.5">{questionConfig.title_en}</p>
        <p className="text-xs text-color-minor">{questionConfig.title_vi}</p>
      </div>
    );
  };

  const renderQuestionDescription = () => {
    const questionConfig = QUESTION_CONFIGS[activeQuestion.question_format];
    const questionDescription = questionConfig.question_description;

    switch (questionDescription) {
      case QuestionDescription.SOUND:
        return (
          <div className="w-full rounded-lg">
            <WavePlayer url={activeQuestion.audio || MOCK_AUDIO} isEnableKeyboard />
          </div>
        );
      case QuestionDescription.IMAGE:
        return (
          <div className="relative z-10 w-full aspect-video">
            <NextImageWithFallback
              src={activeQuestion.image}
              alt=""
              fill
              priority
              objectFit="cover"
            />
          </div>
        );
      case QuestionDescription.MIXED:
        return null;
      default:
        const extra = activeQuestion?.extra || '';
        return (
          <p className="text-color-major text-lg font-normal mt-2.5">
            {extra ? (
              <>
                {activeQuestion.content.split(extra).map((part, index, array) => (
                  <React.Fragment key={index}>
                    {part}
                    {index < array.length - 1 && <strong>{extra}</strong>}
                  </React.Fragment>
                ))}
              </>
            ) : (
              activeQuestion.content
            )}
          </p>
        );
    }
  };

  const renderQuestionContent = () => {
    if (isLoadingAnswers) return <AnswersSkeleton />;
    if (answers.length === 0) return null;

    const questionConfig = QUESTION_CONFIGS[activeQuestion.question_format];
    const answerFormat = questionConfig.answer_format;

    const commonProps = {
      answers,
      setSelectedAnswer,
      selectedAnswer,
      correctAnswers,
    };

    return answerFormat === AnswerFormat.SORT ? (
      <SortQuestion {...commonProps} />
    ) : (
      <NormalQuestion
        activeQuestion={activeQuestion}
        {...commonProps}
        answerFormat={answerFormat}
      />
    );
  };

  const getStatusBackgroundColor = () => {
    switch (questionStatus) {
      case QuestionStatus.CORRECT:
        return 'rgba(0,191,85,0.1)';
      case QuestionStatus.WRONG:
        return 'rgba(235,87,86,0.1)';
      default:
        return 'transparent';
    }
  };

  const statusMessage = () => {
    if (questionStatus === QuestionStatus.CORRECT) {
      return <p className="text-lg text-primary font-medium">{t('learn.correct')}</p>;
    }
    if (questionStatus === QuestionStatus.WRONG) {
      return <p className="text-lg text-red font-medium">{t('learn.wrong')}</p>;
    }
    return null;
  };

  const isCorrectOrWrong =
    questionStatus === QuestionStatus.CORRECT || questionStatus === QuestionStatus.WRONG;
  const buttonDisabled =
    submitAnswerMutation.isPending || (selectedAnswer.length === 0 && !questionStatus);
  const buttonText = isCorrectOrWrong ? t('auth.btnNext') : t('learn.btnCheck');
  const buttonAction = isCorrectOrWrong ? handleNextQuestion : handleCheckAnswer;

  return {
    activeQuestion,
    buttonAction,
    buttonDisabled,
    buttonText,
    correctAnswers,
    getStatusBackgroundColor,
    handleCheckAnswer,
    handleNextQuestion,
    isFinish,
    questionStatus,
    renderQuestionContent,
    renderQuestionDescription,
    renderQuestionTitle,
    selectedAnswer,
    setIsFinish,
    statusMessage,
    submitAnswerMutation,
    handleDoExerciseAgain,
    handleDoNewExercise,
  };
};
