'use client';

import { Button } from '@/components';
import SpeakingControl from './SpeakingControl';
import classNames from 'classnames';
import { useTranslations } from 'next-intl';
import useBalanceStore from '@/store/balance';
import { SentenceProcessEnum } from '@/configs/SentenceProcessEnum';
import { StatusEnum } from '@/configs/StatusEnum';

const SpeakingBottomBar = ({
    isStartMicrophone, speakings, isFinishLearn, activeCharacter, handleStartLearn, handeBackSentence, toggleRecording, setIsPauseSpeak, showTooltipRecord, isPauseSpeak, isSendingRecord, isPauseRecord, sentenceProcess, stopMicrophone
}) => {
    const { balanceStatus } = useBalanceStore();
    const t = useTranslations();
    return (
        <div className={'absolute bottom-0 left-0 w-full right-0 bg-bg-general block'}>
            <div
                className={classNames('z-50 px-4 flex pb-2.5 items-center justify-center', {
                    '!justify-end': speakings.length === 0,
                })}
            >
                {!isFinishLearn && activeCharacter ? (
                    speakings.length === 0 ? (
                        <div className={'w-full text-center mb-4 mt-1'}>
                            <Button
                                onClick={handleStartLearn}
                                color={'primary'}
                                size={'md'}
                                isDisabled={
                                    (sentenceProcess === SentenceProcessEnum.PROCESS && speakings.length > 0) ||
                                    !activeCharacter ||
                                    balanceStatus !== StatusEnum.ON
                                }
                                className={'!inline-flex !w-auto'}
                            >
                                {t('learn.start')}
                            </Button>
                        </div>
                    ) : (
                        <SpeakingControl
                            handeBackSentence={handeBackSentence}
                            toggleRecording={toggleRecording}
                            setIsPauseSpeak={setIsPauseSpeak}
                            showTooltipRecord={showTooltipRecord}
                            isPauseSpeak={isPauseSpeak}
                            isSendingRecord={isSendingRecord}
                            isPaused={isPauseRecord}
                            stopMicrophone={stopMicrophone}
                            isStartMicrophone={isStartMicrophone}
                        />
                    )
                ) : (
                    <div></div>
                )}
            </div>
        </div>
    );
};

export default SpeakingBottomBar;