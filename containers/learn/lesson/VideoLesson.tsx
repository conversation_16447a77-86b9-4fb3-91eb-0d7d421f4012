import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { useConversationContext } from '@/providers/ConversationProvider';
import classNames from 'classnames';
import ReactPlayer from 'react-player/youtube';

import ScrollArea from '@/components/ScrollArea';

export const VideoLesson = () => {
  const { paragraph } = useConversationContext();
  return (
    <ScrollArea
      className={classNames('w-full flex pb-[15px] p-4', {
        'h-[calc(100vh-100px)]': paragraph?.document_id !== SPECIAL_DOCUMENT_ID,
        'h-[calc(100vh-55px)]': paragraph?.document_id === SPECIAL_DOCUMENT_ID,
      })}
      isEnabled={false}
    >
      <div
        className={
          'relative w-full flex justify-center items-center flex-1 bg-[#2a2d3c] rounded-[5px]'
        }
      >
        <div className="w-full aspect-video">
          <ReactPlayer
            url={'https://www.youtube.com/watch?v=82OhhGnenxw'}
            controls={false}
            width="100%"
            height="100%"
            style={{
              pointerEvents: 'none',
            }}
            config={{
              playerVars: {
                showinfo: 0,
                modestbranding: 1,
                controls: 0,
                rel: 0,
                iv_load_policy: 3,
              },
            }}
          />
        </div>
      </div>
    </ScrollArea>
  );
};
