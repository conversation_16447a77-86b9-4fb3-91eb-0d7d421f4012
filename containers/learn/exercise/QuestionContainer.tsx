import { useEffect, useMemo } from 'react';

import Image from 'next/image';

import { Button } from '@/components';
import { QuestionStatus } from '@/configs/Exercise';
import { QUESTION_STATUS_AUDIO, STATUS_IMAGE_URL } from '@/constant';
import audioManager from '@/containers/home/<USER>';
import FinishScreen from '@/containers/learn/FinishScreen';
import { useQuestionKeyboardHandler } from '@/helpers';
import { Question } from '@/interfaces/exercise.interface';
import useLearnStore from '@/store/learn';
import classNames from 'classnames';
import { motion } from 'framer-motion';

import ScrollArea from '@/components/ScrollArea';

import { useQuestion } from '@/hooks/Ent/useQuestion';

type QuestionProps = {
  questions: Question[];
  setIsStarted: React.Dispatch<React.SetStateAction<boolean>>;
  setExerciseProgress: React.Dispatch<React.SetStateAction<number>>;
};

export const QuestionContainer = ({
  questions,
  setIsStarted,
  setExerciseProgress,
}: QuestionProps) => {
  // Set exercise progress to 0 when component loads
  useEffect(() => {
    setExerciseProgress(0);
  }, [setExerciseProgress]);

  const {
    questionStatus,
    handleNextQuestion,
    handleCheckAnswer,
    renderQuestionTitle,
    renderQuestionDescription,
    renderQuestionContent,
    getStatusBackgroundColor,
    buttonDisabled,
    buttonText,
    buttonAction,
    submitAnswerMutation,
    statusMessage,
    isFinish,
    handleDoExerciseAgain,
    handleDoNewExercise,
  } = useQuestion({
    questions,
    setExerciseProgress,
    setIsStarted,
  });

  useQuestionKeyboardHandler(questionStatus, handleNextQuestion, handleCheckAnswer);

  useEffect(() => {
    if (questionStatus === QuestionStatus.CORRECT) {
      audioManager.playAudio(QUESTION_STATUS_AUDIO.CORRECT);
    } else if (questionStatus === QuestionStatus.WRONG) {
      audioManager.playAudio(QUESTION_STATUS_AUDIO.WRONG);
    }
  }, [questionStatus]);

  // Move all hooks above the conditional return
  const rightImageUrl = useMemo(() => {
    return STATUS_IMAGE_URL[Math.random() > 0.5 ? 'NA.RIGHT' : 'XO.RIGHT'];
  }, [questionStatus]);

  const wrongImageUrl = useMemo(() => {
    return STATUS_IMAGE_URL[Math.random() > 0.5 ? 'NA.WRONG' : 'XO.WRONG'];
  }, [questionStatus]);

  const { exerciseToken } = useLearnStore();

  if (isFinish) {
    return (
      <FinishScreen
        isExercise
        onLeanAgain={handleDoExerciseAgain}
        handleDoNewExercise={handleDoNewExercise}
      />
    );
  }

  return (
    <>
      <ScrollArea
        className={classNames(
          'px-[calc((100%_-550px)_/_2)] h-[calc(100vh-200px)] w-full pt-[30px] relative flex flex-col space-y-4',
          {
            '!h-[calc(100vh-152px)]': exerciseToken !== '',
          }
        )}
      >
        {renderQuestionTitle()}
        {renderQuestionDescription()}
        {renderQuestionContent()}
        <div className="w-full h-4" />
      </ScrollArea>
      <div className="w-full h-[75px] absolute z-50 -bottom-1 left-0 bg-bg-general border-t-1 border-solid border-color-line">
        <div
          className="w-full h-full px-[calc((100%_-550px)_/_2)] flex justify-center items-center"
          style={{ backgroundColor: getStatusBackgroundColor() }}
        >
          <div className="flex items-center justify-between w-full relative z-20">
            {statusMessage()}
            <div className="flex flex-col gap-1 h-full ml-auto pt-2 items-center justify-center">
              <Button
                size="md"
                color="primary"
                className="!shadow-[0px_-1px_0px_0px_#01AE4E_inset] ml-2"
                onPress={buttonAction}
                isLoading={submitAnswerMutation.isPending}
                isDisabled={buttonDisabled}
              >
                {buttonText}
              </Button>
              <p className="text-[10px] text-color-minor">Enter</p>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full h-[75px] absolute z-20 bottom-0 left-0 px-[calc((100%_-550px)_/_2)]">
        <div className="relative w-full h-full">
          {questionStatus === QuestionStatus.CORRECT && (
            <motion.div
              initial={{ y: 0, opacity: 0, x: 0 }}
              animate={{ y: -80, opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute left-0"
            >
              <Image src={rightImageUrl} width={111} height={111} alt="correct answer" />
            </motion.div>
          )}
          {questionStatus === QuestionStatus.WRONG && (
            <motion.div
              initial={{ y: 0, opacity: 0, x: 0, zIndex: 0 }}
              animate={{ y: -80, opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute left-0"
            >
              <Image
                src={wrongImageUrl}
                width={111}
                height={111}
                alt="wrong answer"
                objectFit="contain"
              />
            </motion.div>
          )}
        </div>
      </div>
    </>
  );
};
