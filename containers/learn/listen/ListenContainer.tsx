import { useConversationContext } from '@/providers/ConversationProvider';
import { ConversationTypeEnum } from 'configs/ConversationEnum';

import { ListenConversation } from './ListenConversation';
import { ListenEssay } from './ListenEssay';
import { ListenGallery } from './ListenGallery';

export const ListenContainer = () => {
  const { paragraph } = useConversationContext();

  return (
    <>
      <div
        className={`mb-[70px] flex-1 justify-between flex flex-col ${
          paragraph?.item !== ConversationTypeEnum.GALLERY ? 'pl-8' : ''
        } w-full`}
      >
        {paragraph?.item === ConversationTypeEnum.CONVERSATION && <ListenConversation />}
        {paragraph?.item === ConversationTypeEnum.ESSAY && <ListenEssay />}
        {paragraph?.item === ConversationTypeEnum.GALLERY && <ListenGallery />}
      </div>
    </>
  );
};
